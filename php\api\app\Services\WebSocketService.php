<?php

namespace App\Services;

use App\Enums\ApiCodeEnum;
use App\Models\WebSocketSession;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * WebSocket服务
 */
class WebSocketService
{
    /**
     * 认证WebSocket连接
     */
    public function authenticateConnection(int $userId, string $clientType, string $connectionIp, ?string $userAgent = null, ?string $clientVersion = null, array $connectionInfo = []): array
    {
        try {
            DB::beginTransaction();

            // 验证客户端类型
            if (!$this->isValidClientType($clientType)) {
                // 特别处理web_browser类型，返回403禁止访问
                if ($clientType === WebSocketSession::CLIENT_TYPE_WEB_BROWSER) {
                    return [
                        'code' => ApiCodeEnum::FORBIDDEN,
                        'message' => 'Web浏览器不支持WebSocket连接',
                        'data' => []
                    ];
                }

                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '无效的客户端类型',
                    'data' => []
                ];
            }

            // 检查Python工具连接限制
            if ($clientType === WebSocketSession::CLIENT_TYPE_PYTHON_TOOL) {
                $pythonConnections = WebSocketSession::byUser($userId)
                    ->pythonTool()
                    ->active()
                    ->count();

                if ($pythonConnections >= 3) { // 限制每个用户最多3个Python工具连接
                    return [
                        'code' => ApiCodeEnum::CONNECTION_LIMIT,
                        'message' => '用户连接数已达上限',
                        'data' => []
                    ];
                }
            }

            // 生成会话ID
            $sessionId = 'ws_' . Str::random(32);

            // 创建WebSocket会话
            $session = WebSocketSession::create([
                'session_id' => $sessionId,
                'user_id' => $userId,
                'client_type' => $clientType,
                'client_version' => $clientVersion,
                'connection_ip' => $connectionIp,
                'user_agent' => $userAgent,
                'status' => WebSocketSession::STATUS_CONNECTED,
                'connection_info' => $connectionInfo,
                'connected_at' => Carbon::now(),
                'last_ping_at' => Carbon::now()
            ]);

            // 根据客户端类型设置默认订阅事件
            $defaultEvents = $this->getDefaultSubscribedEvents($clientType);
            $session->subscribed_events = $defaultEvents;
            $session->save();

            DB::commit();

            Log::info('WebSocket连接认证成功', [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'client_type' => $clientType,
                'connection_ip' => $connectionIp
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '认证成功',
                'data' => [
                    'session_id' => $sessionId,
                    'websocket_url' => $this->getWebSocketUrl(),
                    'allowed_events' => $defaultEvents,
                    'heartbeat_interval' => 30, // 心跳间隔（秒）
                    'max_idle_time' => 300 // 最大空闲时间（秒）
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('WebSocket连接认证失败', [
                'user_id' => $userId,
                'client_type' => $clientType,
                'connection_ip' => $connectionIp,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '连接认证失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取用户会话列表
     */
    public function getUserSessions(int $userId, ?string $status = null, ?string $clientType = null): array
    {
        try {
            $query = WebSocketSession::byUser($userId);

            if ($status) {
                $query->byStatus($status);
            }

            if ($clientType) {
                $query->byClientType($clientType);
            }

            $sessions = $query->orderBy('connected_at', 'desc')->get();

            $sessionsData = $sessions->map(function ($session) {
                return [
                    'session_id' => $session->session_id,
                    'client_type' => $session->client_type,
                    'client_version' => $session->client_version,
                    'status' => $session->status,
                    'connection_ip' => $session->connection_ip,
                    'subscribed_events' => $session->subscribed_events ?? [],
                    'connected_at' => $session->connected_at->format('Y-m-d H:i:s'),
                    'last_ping_at' => $session->last_ping_at?->format('Y-m-d H:i:s'),
                    'disconnected_at' => $session->disconnected_at?->format('Y-m-d H:i:s'),
                    'message_count' => $session->message_count,
                    'connection_duration' => $session->getConnectionDuration(),
                    'is_active' => $session->isActive()
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $sessionsData->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('获取用户会话列表失败', [
                'user_id' => $userId,
                'status' => $status,
                'client_type' => $clientType,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取会话列表失败',
                'data' => []
            ];
        }
    }

    /**
     * 断开会话连接
     */
    public function disconnectSession(string $sessionId, int $userId, string $reason = '用户主动断开'): array
    {
        try {
            $session = WebSocketSession::where('session_id', $sessionId)
                ->where('user_id', $userId)
                ->first();

            if (!$session) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '会话不存在',
                    'data' => []
                ];
            }

            if ($session->status !== WebSocketSession::STATUS_CONNECTED) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '会话已断开',
                    'data' => []
                ];
            }

            $session->disconnect($reason);

            // 通知WebSocket服务器断开连接
            $this->notifyServerDisconnect($sessionId);

            Log::info('WebSocket会话断开成功', [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'reason' => $reason
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '连接已断开',
                'data' => [
                    'session_id' => $sessionId,
                    'disconnected_at' => $session->disconnected_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('断开WebSocket会话失败', [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '断开连接失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取服务器状态
     */
    public function getServerStatus(): array
    {
        try {
            $cacheKey = 'websocket_server_status';
            $cached = Cache::get($cacheKey);
            
            if ($cached) {
                return $cached;
            }

            // 统计连接数据
            $totalConnections = WebSocketSession::count();
            $activeConnections = WebSocketSession::active()->count();
            $pythonConnections = WebSocketSession::active()->pythonTool()->count();
            $webConnections = WebSocketSession::active()->byClientType('web_browser')->count();

            // 计算服务器运行时间（模拟）
            $serverStartTime = Cache::get('websocket_server_start_time', Carbon::now()->subHours(2));
            $uptime = $serverStartTime->diffForHumans(Carbon::now(), true);

            // 统计消息数量
            $totalMessages = WebSocketSession::sum('message_count');

            $result = [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'server_status' => 'running',
                    'total_connections' => $totalConnections,
                    'active_connections' => $activeConnections,
                    'python_tool_connections' => $pythonConnections,
                    'web_browser_connections' => $webConnections,
                    'server_uptime' => $uptime,
                    'total_messages_sent' => $totalMessages,
                    'websocket_url' => $this->getWebSocketUrl(),
                    'last_check' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

            // 缓存1分钟
            Cache::put($cacheKey, $result, 60);

            return $result;

        } catch (\Exception $e) {
            Log::error('获取WebSocket服务器状态失败', [
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取服务器状态失败',
                'data' => []
            ];
        }
    }

    /**
     * 推送消息到指定会话
     */
    public function pushMessage(string $sessionId, string $eventType, array $data): bool
    {
        try {
            $session = WebSocketSession::where('session_id', $sessionId)->first();
            
            if (!$session || !$session->isActive()) {
                return false;
            }

            if (!$session->isSubscribedTo($eventType)) {
                return false;
            }

            $message = [
                'event' => $eventType,
                'data' => $data,
                'timestamp' => Carbon::now()->toISOString(),
                'session_id' => $sessionId
            ];

            // 这里应该调用实际的WebSocket服务器推送消息
            // 目前模拟推送成功
            $this->simulatePushMessage($sessionId, $message);

            $session->incrementMessageCount();

            Log::info('WebSocket消息推送成功', [
                'session_id' => $sessionId,
                'event_type' => $eventType,
                'data_size' => strlen(json_encode($data))
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('WebSocket消息推送失败', [
                'session_id' => $sessionId,
                'event_type' => $eventType,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 推送消息到用户的所有活跃会话
     */
    public function pushToUser(int $userId, string $eventType, array $data): int
    {
        $sessions = WebSocketSession::byUser($userId)->active()->get();
        $successCount = 0;

        foreach ($sessions as $session) {
            if ($this->pushMessage($session->session_id, $eventType, $data)) {
                $successCount++;
            }
        }

        return $successCount;
    }

    /**
     * 清理超时连接
     */
    public function cleanupTimeoutSessions(): int
    {
        $timeoutSessions = WebSocketSession::timeout()->get();
        $cleanedCount = 0;

        foreach ($timeoutSessions as $session) {
            $session->markAsTimeout();
            $cleanedCount++;
        }

        Log::info('清理超时WebSocket连接', [
            'cleaned_count' => $cleanedCount
        ]);

        return $cleanedCount;
    }

    /**
     * 验证客户端类型是否有效
     * 根据index.mdc规范，WebSocket仅限Python工具和移动应用使用
     */
    private function isValidClientType(string $clientType): bool
    {
        return in_array($clientType, [
            WebSocketSession::CLIENT_TYPE_PYTHON_TOOL,
            WebSocketSession::CLIENT_TYPE_MOBILE_APP
            // 移除 WebSocketSession::CLIENT_TYPE_WEB_BROWSER 以符合规范
        ]);
    }

    /**
     * 获取默认订阅事件
     */
    private function getDefaultSubscribedEvents(string $clientType): array
    {
        switch ($clientType) {
            case WebSocketSession::CLIENT_TYPE_PYTHON_TOOL:
                return [
                    WebSocketSession::EVENT_AI_GENERATION_PROGRESS,
                    WebSocketSession::EVENT_AI_GENERATION_COMPLETED,
                    WebSocketSession::EVENT_AI_GENERATION_FAILED
                ];
            
            case WebSocketSession::CLIENT_TYPE_MOBILE_APP:
                return [
                    WebSocketSession::EVENT_AI_GENERATION_PROGRESS,
                    WebSocketSession::EVENT_AI_GENERATION_COMPLETED,
                    WebSocketSession::EVENT_AI_GENERATION_FAILED,
                    WebSocketSession::EVENT_POINTS_CHANGED
                ];
            
            default:
                return [
                    WebSocketSession::EVENT_AI_GENERATION_COMPLETED,
                    WebSocketSession::EVENT_POINTS_CHANGED
                ];
        }
    }

    /**
     * 获取WebSocket连接URL
     */
    private function getWebSocketUrl(): string
    {
        return config('websocket.url');
    }

    /**
     * 通知服务器断开连接
     */
    private function notifyServerDisconnect(string $sessionId): void
    {
        // 这里应该通知实际的WebSocket服务器断开指定连接
        // 目前模拟通知
        Log::info('通知WebSocket服务器断开连接', ['session_id' => $sessionId]);
    }

    /**
     * 模拟推送消息
     */
    private function simulatePushMessage(string $sessionId, array $message): void
    {
        // 模拟推送消息到WebSocket服务器
        Log::info('模拟WebSocket消息推送', [
            'session_id' => $sessionId,
            'message' => $message
        ]);
    }

    /**
     * 生成WebSocket认证令牌
     * 第2F阶段：新增方法
     */
    public function generateAuthToken(int $userId, array $authParams): array
    {
        try {
            DB::beginTransaction();

            // 生成认证令牌
            $authToken = 'ws_auth_' . Str::random(32);

            // 计算过期时间
            $expiresAt = Carbon::now()->addSeconds($authParams['expires_in']);

            // 存储认证信息到缓存
            $authData = [
                'user_id' => $userId,
                'channel' => $authParams['channel'],
                'permissions' => $authParams['permissions'],
                'client_info' => $authParams['client_info'],
                'created_at' => Carbon::now()->toISOString(),
                'expires_at' => $expiresAt->toISOString()
            ];

            Cache::put(
                'ws_auth:' . $authToken,
                $authData,
                $authParams['expires_in']
            );

            // 记录认证日志
            Log::info('WebSocket认证令牌生成', [
                'user_id' => $userId,
                'channel' => $authParams['channel'],
                'permissions' => $authParams['permissions'],
                'expires_at' => $expiresAt->format('Y-m-d H:i:s'),
                'client_info' => $authParams['client_info']
            ]);

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'WebSocket认证令牌生成成功',
                'data' => [
                    'auth_token' => $authToken,
                    'websocket_url' => $this->getWebSocketUrl(),
                    'channel' => $authParams['channel'],
                    'user_id' => $userId,
                    'permissions' => $authParams['permissions'],
                    'expires_at' => $expiresAt->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('WebSocket认证令牌生成失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => 'WebSocket认证令牌生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }
}
