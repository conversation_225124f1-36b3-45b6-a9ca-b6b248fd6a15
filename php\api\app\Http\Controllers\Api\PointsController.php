<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\PointsTransaction;
use App\Services\PointsService;
use App\Services\AuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 用户积分管理与交易记录
 */
class PointsController extends Controller
{
    protected $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * @ApiTitle(积分余额查询)
     * @ApiSummary(获取用户当前积分余额)
     * @ApiMethod(GET)
     * @ApiRoute(/api/points/balance)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.points", type="decimal", required=true, description="可用积分")
     * @ApiReturnParams (name="data.frozen_points", type="decimal", required=true, description="冻结积分")
     * @ApiReturnParams (name="data.total_points", type="decimal", required=true, description="总积分")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "points": "100.50",
     *     "frozen_points": "20.00",
     *     "total_points": "120.50"
     *   }
     * })
     */
    public function balance(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            return $this->successResponse([
                'points' => $user->points,
                'frozen_points' => $user->frozen_points,
                'total_points' => $user->points + $user->frozen_points
            ], 'success');
        } catch (\Exception $e) {
            Log::error('获取积分余额失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取积分余额失败');
        }
    }

    /**
     * @ApiTitle(积分充值)
     * @ApiSummary(用户积分充值)
     * @ApiMethod(POST)
     * @ApiRoute(/api/points/recharge)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="amount", type="decimal", required=true, description="充值金额")
     * @ApiParams(name="payment_method", type="string", required=true, description="支付方式")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.transaction_id", type="int", required=true, description="交易ID")
     * @ApiReturnParams (name="data.amount", type="decimal", required=true, description="充值金额")
     * @ApiReturnParams (name="data.new_balance", type="decimal", required=true, description="新的积分余额")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "充值成功",
     *   "data": {
     *     "transaction_id": 123,
     *     "amount": "100.00",
     *     "new_balance": "200.50"
     *   }
     * })
     */
    public function recharge(Request $request)
    {
        try {
            // 🔧 LongChec2修复：先进行认证，再验证参数
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'amount' => 'required|numeric|min:1|max:10000',
                'payment_method' => 'required|string|in:alipay,wechat,bank_card'
            ];

            $messages = [
                'amount.required' => '充值金额不能为空',
                'amount.numeric' => '充值金额必须为数字',
                'amount.min' => '充值金额不能少于1元',
                'amount.max' => '充值金额不能超过10000元',
                'payment_method.required' => '支付方式不能为空',
                'payment_method.in' => '支付方式无效'
            ];

            $this->validateData($request->all(), $rules, $messages, []);
            $result = $this->pointsService->recharge($user->id, $request->amount, $request->payment_method);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('充值失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '充值失败');
        }
    }

    /**
     * @ApiTitle(积分交易记录)
     * @ApiSummary(获取用户积分交易记录)
     * @ApiMethod(GET)
     * @ApiRoute(/api/points/transactions)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiParams(name="status", type="string", required=false, description="状态筛选")
     * @ApiParams(name="business_type", type="string", required=false, description="业务类型筛选")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.transactions", type="array", required=true, description="交易记录列表")
     * @ApiReturnParams (name="data.pagination", type="object", required=true, description="分页信息")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "transactions": [
     *       {
     *         "id": 1,
     *         "business_type": "recharge",
     *         "amount": "100.00",
     *         "status": "success",
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 50,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function transactions(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $page = $request->get('page', 1);
            $perPage = min($request->get('per_page', 20), 100);

            $query = PointsTransaction::where('user_id', $user->id);

            // 状态筛选
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // 业务类型筛选
            if ($request->has('business_type')) {
                $query->where('business_type', $request->business_type);
            }

            $transactions = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            return $this->successResponse([
                'transactions' => $transactions->items(),
                'pagination' => [
                    'current_page' => $transactions->currentPage(),
                    'total' => $transactions->total(),
                    'per_page' => $transactions->perPage(),
                    'last_page' => $transactions->lastPage()
                ]
            ], 'success');
        } catch (\Exception $e) {
            Log::error('获取积分交易记录失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取积分交易记录失败');
        }
    }
}
