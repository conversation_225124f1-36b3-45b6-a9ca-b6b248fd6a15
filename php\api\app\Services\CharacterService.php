<?php

namespace App\Services;

use App\Enums\ApiCodeEnum;
use App\Models\CharacterCategory;
use App\Models\CharacterLibrary;
use App\Models\UserCharacterBinding;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

/**
 * 角色管理服务
 */
class CharacterService
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }
    
    /**
     * 获取角色分类列表 
     */
    public function getCategories(?int $parentId = null, bool $includeChildren = false): array
    {
        try {
            $query = CharacterCategory::active();

            if ($parentId !== null) {
                $query->children($parentId);
            } else {
                $query->root();
            }

            $categories = $query->ordered()->get();

            $categoriesData = $categories->map(function ($category) use ($includeChildren) {
                $data = [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'icon' => $category->icon,
                    'color' => $category->color,
                    'character_count' => $category->character_count,
                    'sort_order' => $category->sort_order
                ];

                if ($includeChildren && $category->hasChildren()) {
                    $data['children'] = $category->children()->active()->ordered()->get()->map(function ($child) {
                        return [
                            'id' => $child->id,
                            'name' => $child->name,
                            'slug' => $child->slug,
                            'character_count' => $child->character_count
                        ];
                    })->toArray();
                }

                return $data;
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $categoriesData->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('获取角色分类失败', [
                'parent_id' => $parentId,
                'include_children' => $includeChildren,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取分类失败',
                'data' => []
            ];
        }
    }

    /**
     * 绑定角色 (新版本 - 支持reason参数)
     */
    public function bindCharacter(int $userId, int $characterId, string $reason, ?string $bindingName = null, ?string $customDescription = null, array $customConfig = []): array
    {
        try {
            DB::beginTransaction();

            // 检查角色是否存在
            $character = CharacterLibrary::active()->find($characterId);
            if (!$character) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '角色不存在',
                    'data' => []
                ];
            }

            // 检查是否已经绑定
            $existingBinding = UserCharacterBinding::byUser($userId)
                ->byCharacter($characterId)
                ->first();

            if ($existingBinding) {
                if ($existingBinding->is_active) {
                    return [
                        'code' => ApiCodeEnum::FAIL,
                        'message' => '角色已经绑定',
                        'data' => []
                    ];
                } else {
                    // 重新激活绑定
                    $existingBinding->is_active = true;
                    $existingBinding->binding_name = $bindingName;
                    $existingBinding->custom_description = $customDescription;
                    $existingBinding->custom_config = $customConfig;
                    $existingBinding->save();

                    $character->incrementBinding();
                    DB::commit();

                    return [
                        'code' => ApiCodeEnum::SUCCESS,
                        'message' => '角色重新绑定成功',
                        'data' => [
                            'binding_id' => $existingBinding->id,
                            'character_id' => $character->id,
                            'character_name' => $character->name,
                            'binding_name' => $existingBinding->getDisplayName(),
                            'created_at' => $existingBinding->created_at->format('Y-m-d H:i:s')
                        ]
                    ];
                }
            }

            // 创建新绑定 (使用reason参数)
            $binding = UserCharacterBinding::create([
                'user_id' => $userId,
                'character_id' => $characterId,
                'binding_name' => $bindingName ?: $character->name, // 默认使用角色名称
                'binding_reason' => $reason, // 新增：绑定原因
                'custom_description' => $customDescription,
                'custom_config' => $customConfig,
                'is_active' => true
            ]);

            // 增加角色绑定次数
            $character->incrementBinding();

            DB::commit();

            Log::info('角色绑定成功', [
                'user_id' => $userId,
                'character_id' => $characterId,
                'binding_id' => $binding->id
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色绑定成功',
                'data' => [
                    'binding_id' => $binding->id,
                    'character_id' => $character->id,
                    'character_name' => $character->name,
                    'binding_name' => $binding->getDisplayName(),
                    'created_at' => $binding->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('角色绑定失败', [
                'user_id' => $userId,
                'character_id' => $characterId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色绑定失败',
                'data' => []
            ];
        }
    }

    /**
     * 解绑角色
     */
    public function unbindCharacter(int $bindingId, int $userId): array
    {
        try {
            DB::beginTransaction();

            $binding = UserCharacterBinding::byUser($userId)->find($bindingId);
            if (!$binding) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '绑定不存在',
                    'data' => []
                ];
            }

            if (!$binding->is_active) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '绑定已解除',
                    'data' => []
                ];
            }

            // 解除绑定
            $binding->is_active = false;
            $binding->save();

            // 减少角色绑定次数
            $binding->character->decrementBinding();

            DB::commit();

            Log::info('角色解绑成功', [
                'user_id' => $userId,
                'binding_id' => $bindingId,
                'character_id' => $binding->character_id
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色解绑成功',
                'data' => [
                    'binding_id' => $bindingId,
                    'unbind_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('角色解绑失败', [
                'user_id' => $userId,
                'binding_id' => $bindingId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色解绑失败',
                'data' => []
            ];
        }
    }

    /**
     * 更新绑定
     */
    public function updateBinding(int $bindingId, int $userId, array $updateData): array
    {
        try {
            $binding = UserCharacterBinding::byUser($userId)->find($bindingId);
            if (!$binding) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '绑定不存在',
                    'data' => []
                ];
            }

            $binding->fill($updateData);
            $binding->save();

            Log::info('绑定更新成功', [
                'user_id' => $userId,
                'binding_id' => $bindingId,
                'update_data' => array_keys($updateData)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '绑定更新成功',
                'data' => [
                    'binding_id' => $binding->id,
                    'binding_name' => $binding->getDisplayName(),
                    'is_favorite' => $binding->is_favorite,
                    'updated_at' => $binding->updated_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('绑定更新失败', [
                'user_id' => $userId,
                'binding_id' => $bindingId,
                'update_data' => $updateData,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '绑定更新失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取用户绑定列表
     */
    public function getUserBindings(int $userId, array $filters, int $page, int $perPage): array
    {
        try {
            $query = UserCharacterBinding::with(['character.category'])
                ->byUser($userId)
                ->active();

            // 应用筛选条件
            if (isset($filters['is_favorite']) && $filters['is_favorite']) {
                $query->favorite();
            }

            if (!empty($filters['category_id'])) {
                $query->whereHas('character', function ($q) use ($filters) {
                    $q->where('category_id', $filters['category_id']);
                });
            }

            // 排序
            switch ($filters['sort'] ?? 'usage') {
                case 'rating':
                    $query->byRating();
                    break;
                case 'created':
                    $query->orderBy('created_at', 'desc');
                    break;
                default:
                    $query->byUsage();
                    break;
            }

            $bindings = $query->paginate($perPage, ['*'], 'page', $page);

            $bindingsData = $bindings->map(function ($binding) {
                return [
                    'id' => $binding->id,
                    'character' => [
                        'id' => $binding->character->id,
                        'name' => $binding->character->name,
                        'avatar' => $binding->character->avatar,
                        'category' => $binding->character->category->name,
                        'rating' => $binding->character->rating
                    ],
                    'binding_name' => $binding->getDisplayName(),
                    'custom_description' => $binding->custom_description,
                    'is_favorite' => $binding->is_favorite,
                    'usage_count' => $binding->usage_count,
                    'last_used_at' => $binding->last_used_at?->format('Y-m-d H:i:s'),
                    'user_rating' => $binding->user_rating,
                    'usage_frequency' => $binding->getUsageFrequency(),
                    'created_at' => $binding->created_at->format('Y-m-d H:i:s')
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'bindings' => $bindingsData,
                    'pagination' => [
                        'current_page' => $bindings->currentPage(),
                        'total' => $bindings->total(),
                        'per_page' => $bindings->perPage(),
                        'last_page' => $bindings->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户绑定列表失败', [
                'user_id' => $userId,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取绑定列表失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取角色列表
     */
    public function getCharacters(array $filters, int $page, int $perPage): array
    {
        try {
            $query = CharacterLibrary::with('category')->active();

            // 应用筛选条件
            // tab参数处理：public/private
            if (!empty($filters['tab']) && $filters['tab'] === 'private') {
                // 私人角色筛选逻辑（基于created_by字段）
                $query->whereNotNull('created_by');
            } else {
                // 公开角色（系统预设角色，created_by为null）
                $query->whereNull('created_by');
            }

            if (!empty($filters['gender'])) {
                $query->byGender($filters['gender']);
            }

            if (!empty($filters['age_range'])) {
                $query->where('age_range', $filters['age_range']);
            }

            if (!empty($filters['search'])) {
                $query->search($filters['search']);
            }

            if (isset($filters['is_premium'])) {
                if ($filters['is_premium']) {
                    $query->premium();
                } else {
                    $query->free();
                }
            }

            if (isset($filters['is_featured']) && $filters['is_featured']) {
                $query->featured();
            }

            if (!empty($filters['tags'])) {
                foreach ($filters['tags'] as $tag) {
                    $query->byTag($tag);
                }
            }

            $characters = $query->ordered()->paginate($perPage, ['*'], 'page', $page);

            $charactersData = $characters->map(function ($character) {
                return [
                    'id' => $character->id,
                    'name' => $character->name,
                    'description' => $character->description,
                    'category' => $character->category->name,
                    'gender' => $character->gender,
                    'age_range' => $character->age_range,
                    'avatar' => $character->avatar,
                    'tags' => $character->tags ?? [],
                    'is_premium' => $character->is_premium,
                    'is_featured' => $character->is_featured,
                    'rating' => $character->rating,
                    'rating_count' => $character->rating_count,
                    'binding_count' => $character->binding_count
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'characters' => $charactersData,
                    'pagination' => [
                        'current_page' => $characters->currentPage(),
                        'total' => $characters->total(),
                        'per_page' => $characters->perPage(),
                        'last_page' => $characters->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取角色列表失败', [
                'filters' => $filters,
                'page' => $page,
                'per_page' => $perPage,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取角色列表失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取角色详情 
     */
    public function getCharacterDetail(int $characterId, int $userId): array
    {
        try {
            $character = CharacterLibrary::with('category')->active()->find($characterId);

            if (!$character) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '角色不存在',
                    'data' => []
                ];
            }

            // 检查用户是否已绑定该角色
            $binding = UserCharacterBinding::byUser($userId)
                ->byCharacter($characterId)
                ->active()
                ->first();

            $characterData = [
                'id' => $character->id,
                'name' => $character->name,
                'description' => $character->description,
                'category' => [
                    'id' => $character->category->id,
                    'name' => $character->category->name,
                    'slug' => $character->category->slug
                ],
                'gender' => $character->gender,
                'age_range' => $character->age_range,
                'personality' => $character->personality,
                'background' => $character->background,
                'appearance' => $character->appearance,
                'avatar' => $character->avatar,
                'images' => $character->images ?? [],
                'voice_config' => $character->voice_config ?? [],
                'style_preferences' => $character->style_preferences ?? [],
                'tags' => $character->tags ?? [],
                'is_premium' => $character->is_premium,
                'is_featured' => $character->is_featured,
                'rating' => $character->rating,
                'rating_count' => $character->rating_count,
                'binding_count' => $character->binding_count,
                'is_bound' => !is_null($binding),
                'binding_id' => $binding?->id,
                'created_at' => $character->created_at->format('Y-m-d H:i:s')
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $characterData
            ];

        } catch (\Exception $e) {
            Log::error('获取角色详情失败', [
                'character_id' => $characterId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取角色详情失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取推荐角色
     */
    public function getRecommendations(int $userId, int $limit, string $type): array
    {
        try {
            $query = CharacterLibrary::with('category')->active();

            switch ($type) {
                case 'popular':
                    $characters = $query->popular($limit)->get();
                    break;

                case 'similar':
                    $characters = $this->getSimilarCharacters($userId, $limit);
                    break;

                case 'new':
                    $characters = $query->orderBy('created_at', 'desc')->limit($limit)->get();
                    break;

                default:
                    $characters = $query->featured()->limit($limit)->get();
                    break;
            }

            $recommendationsData = $characters->map(function ($character) use ($type) {
                return [
                    'id' => $character->id,
                    'name' => $character->name,
                    'description' => $character->description,
                    'category' => $character->category->name,
                    'avatar' => $character->avatar,
                    'rating' => $character->rating,
                    'binding_count' => $character->binding_count,
                    'reason' => $this->getRecommendationReason($type)
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'recommendations' => $recommendationsData,
                    'recommendation_type' => $type,
                    'total' => $recommendationsData->count()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取推荐角色失败', [
                'user_id' => $userId,
                'limit' => $limit,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取推荐失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取相似角色
     */
    private function getSimilarCharacters(int $userId, int $limit)
    {
        // 获取用户已绑定的角色
        $userBindings = UserCharacterBinding::byUser($userId)
            ->active()
            ->with('character.category')
            ->get();

        if ($userBindings->isEmpty()) {
            // 如果用户没有绑定角色，返回推荐角色
            return CharacterLibrary::with('category')->active()->featured()->limit($limit)->get();
        }

        // 获取用户偏好的分类
        $preferredCategories = $userBindings->pluck('character.category_id')->unique();

        // 基于偏好推荐相似角色
        $query = CharacterLibrary::with('category')->active()
            ->whereNotIn('id', $userBindings->pluck('character_id'));

        if ($preferredCategories->isNotEmpty()) {
            $query->whereIn('category_id', $preferredCategories);
        }

        return $query->limit($limit)->get();
    }

    /**
     * 获取推荐理由
     */
    private function getRecommendationReason(string $type): string
    {
        $reasons = [
            'popular' => '热门推荐',
            'similar' => '基于您的偏好推荐',
            'new' => '最新角色',
            'featured' => '精选推荐'
        ];

        return $reasons[$type] ?? '系统推荐';
    }

    /**
     * 生成角色
     * 第2D1阶段：角色生成功能
     */
    public function generateCharacter(int $userId, string $prompt, ?int $projectId = null, array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取模型配置
            $platform = $generationParams['platform'] ?? 'deepseek';
            $model = $this->aiModelService->getModelByPlatform($platform, AiModelConfig::TYPE_TEXT_GENERATION);

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的角色生成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '角色生成服务当前不可用',
                    'data' => []
                ];
            }

            // 构建增强提示词
            $enhancedPrompt = $this->buildCharacterPrompt($prompt, $generationParams);

            // 计算预估成本
            $estimatedCost = $this->calculateCharacterCost($model, $enhancedPrompt);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'character_generation',
                null,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => AiGenerationTask::TYPE_CHARACTER_GENERATION,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'enhanced_prompt' => $enhancedPrompt,
                    'character_type' => $generationParams['character_type'] ?? null,
                    'gender' => $generationParams['gender'] ?? null,
                    'age_range' => $generationParams['age_range'] ?? null
                ],
                'generation_params' => $generationParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行生成任务
            $this->executeCharacterGeneration($task);

            Log::info('角色生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色生成任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'platform' => $platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('角色生成失败', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 构建角色提示词
     */
    private function buildCharacterPrompt(string $prompt, array $params): string
    {
        $enhancedPrompt = "请生成一个详细的角色描述，包含以下信息：\n";
        $enhancedPrompt .= "基础要求：" . $prompt . "\n\n";

        // 添加性别要求
        if (!empty($params['gender'])) {
            $genderMap = [
                'male' => '男性',
                'female' => '女性',
                'other' => '其他性别'
            ];
            $enhancedPrompt .= "性别：" . $genderMap[$params['gender']] . "\n";
        }

        // 添加年龄要求
        if (!empty($params['age_range'])) {
            $enhancedPrompt .= "年龄范围：" . $params['age_range'] . "\n";
        }

        // 添加角色类型要求
        if (!empty($params['character_type'])) {
            $enhancedPrompt .= "角色类型：" . $params['character_type'] . "\n";
        }

        $enhancedPrompt .= "\n请按以下格式生成角色信息：\n";
        $enhancedPrompt .= "姓名：[角色姓名]\n";
        $enhancedPrompt .= "描述：[角色的基本描述]\n";
        $enhancedPrompt .= "性格：[性格特点]\n";
        $enhancedPrompt .= "背景：[角色背景故事]\n";
        $enhancedPrompt .= "外观：[外观特征描述]\n";
        $enhancedPrompt .= "技能：[角色拥有的技能或能力]\n";
        $enhancedPrompt .= "关系：[与其他角色的关系]";

        return $enhancedPrompt;
    }

    /**
     * 计算角色生成成本
     */
    private function calculateCharacterCost(AiModelConfig $model, string $prompt): float
    {
        $baseTokens = strlen($prompt) / 4; // 估算token数
        $estimatedTokens = $baseTokens * 1.2; // 角色生成相对简单
        return round($estimatedTokens * $model->cost_per_token, 4);
    }

    /**
     * 执行角色生成
     */
    private function executeCharacterGeneration(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 调用AI服务
            $result = $this->callAiService($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'character_generation', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $this->pointsService->refundPoints($task->user_id, $task->cost, 'character_generation_failed', $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'character_generation_error', $task->id);

            Log::error('角色生成执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 调用AI服务
     */
    private function callAiService(AiGenerationTask $task): array
    {
        try {
            $aiApiUrl = config('ai.api_url', 'https://aiapi.tiptop.cn');
            $platformConfig = config("ai.platforms.{$task->platform}");
            $endpoint = $platformConfig['endpoint'] ?? '/deepseek/chat/completions';

            $timeout = $platformConfig['timeout'] ?? config('ai.timeout', 30);
            $response = Http::timeout($timeout)->post($aiApiUrl . $endpoint, [
                'model' => $task->model_name,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $task->input_data['enhanced_prompt']
                    ]
                ],
                'temperature' => 0.8,
                'max_tokens' => 1000
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $content = $data['choices'][0]['message']['content'] ?? '';

                return [
                    'success' => true,
                    'data' => [
                        'character_info' => $this->parseCharacterInfo($content)
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 解析角色信息
     */
    private function parseCharacterInfo(string $content): array
    {
        $characterInfo = [
            'name' => '',
            'description' => '',
            'personality' => '',
            'background' => '',
            'appearance' => '',
            'skills' => [],
            'relationships' => []
        ];

        // 简单的文本解析逻辑
        $lines = explode("\n", $content);
        foreach ($lines as $line) {
            $line = trim($line);
            if (preg_match('/^姓名[：:]\s*(.+)/', $line, $matches)) {
                $characterInfo['name'] = trim($matches[1]);
            } elseif (preg_match('/^描述[：:]\s*(.+)/', $line, $matches)) {
                $characterInfo['description'] = trim($matches[1]);
            } elseif (preg_match('/^性格[：:]\s*(.+)/', $line, $matches)) {
                $characterInfo['personality'] = trim($matches[1]);
            } elseif (preg_match('/^背景[：:]\s*(.+)/', $line, $matches)) {
                $characterInfo['background'] = trim($matches[1]);
            } elseif (preg_match('/^外观[：:]\s*(.+)/', $line, $matches)) {
                $characterInfo['appearance'] = trim($matches[1]);
            } elseif (preg_match('/^技能[：:]\s*(.+)/', $line, $matches)) {
                $skills = explode('、', trim($matches[1]));
                $characterInfo['skills'] = array_map('trim', $skills);
            } elseif (preg_match('/^关系[：:]\s*(.+)/', $line, $matches)) {
                $characterInfo['relationships'] = [trim($matches[1])];
            }
        }

        // 如果解析失败，使用原始内容作为描述
        if (empty($characterInfo['name']) && empty($characterInfo['description'])) {
            $characterInfo['description'] = $content;
            $characterInfo['name'] = '生成的角色';
        }

        return $characterInfo;
    }
}
