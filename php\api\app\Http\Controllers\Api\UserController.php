<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Models\UserPreference;
use App\Services\UserService;
use App\Services\AuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 用户信息管理与偏好设置
 */
class UserController extends Controller
{
    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    /**
     * @ApiTitle(用户中心信息)
     * @ApiSummary(获取用户个人信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/user/profile)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.id", type="int", required=true, description="用户ID")
     * @ApiReturnParams (name="data.username", type="string", required=true, description="用户名")
     * @ApiReturnParams (name="data.nickname", type="string", required=false, description="昵称")
     * @ApiReturnParams (name="data.email", type="string", required=false, description="邮箱")
     * @ApiReturnParams (name="data.avatar", type="string", required=false, description="头像URL")
     * @ApiReturnParams (name="data.points", type="decimal", required=true, description="积分余额")
     * @ApiReturnParams (name="data.is_vip", type="boolean", required=true, description="是否VIP")
     * @ApiReturnParams (name="data.vip_expires_at", type="string", required=false, description="VIP过期时间")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 1,
     *     "username": "testuser",
     *     "nickname": "测试用户",
     *     "email": "<EMAIL>",
     *     "avatar": "https://example.com/avatar.jpg",
     *     "points": "100.50",
     *     "is_vip": true,
     *     "vip_expires_at": "2024-12-31 23:59:59"
     *   }
     * })
     */
    public function profile(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $data = [
                'id' => $user->id,
                'username' => $user->username,
                'nickname' => $user->nickname,
                'email' => $user->email,
                'avatar' => $user->avatar,
                'points' => $user->points,
                'frozen_points' => $user->frozen_points,
                'is_vip' => $user->is_vip,
                'vip_expires_at' => $user->vip_expires_at ? $user->vip_expires_at->format('Y-m-d H:i:s') : null,
                'last_login_at' => $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : null,
                'created_at' => $user->created_at ? $user->created_at->format('Y-m-d H:i:s') : null
            ];

            return $this->successResponse($data, 'success');
        } catch (\Exception $e) {
            Log::error('获取用户信息失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取用户信息失败');
        }
    }

    /**
     * 更新用户资料
     * 修复500错误 - 添加缺失的updateProfile方法
     *
     * @param Request $request
     * @return array
     */
    public function updateProfile(Request $request)
    {
        try {
            // 验证用户身份
            $authResult = AuthService::authenticate($request);

            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 验证请求参数
            $this->validateData($request->all(), [
                'nickname' => 'sometimes|string|max:50',
                'email' => 'sometimes|email|max:100',
                'avatar' => 'sometimes|string|max:255'
            ]);

            // 更新用户资料
            $updateData = [];
            if ($request->has('nickname')) {
                $updateData['nickname'] = $request->input('nickname');
            }
            if ($request->has('email')) {
                $updateData['email'] = $request->input('email');
            }
            if ($request->has('avatar')) {
                $updateData['avatar'] = $request->input('avatar');
            }

            if (!empty($updateData)) {
                $result = $this->userService->updateProfile($user->id, $updateData);
                if ($result['code'] !== ApiCodeEnum::SUCCESS) {
                    return $this->errorResponse($result['code'], $result['message']);
                }
            }

            return $this->successResponse([], '用户资料更新成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        } catch (\Exception $e) {
            Log::error('个人信息更新失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '个人信息更新失败');
        }
    }

    /**
     * @ApiTitle(用户偏好设置)
     * @ApiSummary(更新用户偏好设置)
     * @ApiMethod(PUT)
     * @ApiRoute(/api/user/preferences)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="language", type="string", required=false, description="语言偏好")
     * @ApiParams(name="timezone", type="string", required=false, description="时区")
     * @ApiParams(name="email_notifications", type="boolean", required=false, description="邮件通知")
     * @ApiParams(name="push_notifications", type="boolean", required=false, description="推送通知")
     * @ApiParams(name="ai_preferences", type="object", required=false, description="AI偏好设置")
     * @ApiParams(name="ui_preferences", type="object", required=false, description="UI偏好设置")
     * @ApiParams(name="workflow_preferences", type="object", required=false, description="工作流偏好")
     * @ApiParams(name="default_ai_model", type="string", required=false, description="默认AI模型")
     * @ApiParams(name="auto_save_interval", type="int", required=false, description="自动保存间隔(秒)")
     * @ApiParams(name="show_tutorials", type="boolean", required=false, description="显示教程")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "偏好设置更新成功",
     *   "data": {
     *     "language": "zh-CN",
     *     "timezone": "Asia/Shanghai",
     *     "email_notifications": true,
     *     "push_notifications": true
     *   }
     * })
     */
    public function updatePreferences(Request $request)
    {
        try {
            $rules = [
                'language' => 'sometimes|string|in:zh-CN,en-US,ja-JP',
                'timezone' => 'sometimes|string|timezone',
                'email_notifications' => 'sometimes|boolean',
                'push_notifications' => 'sometimes|boolean',
                'ai_preferences' => 'sometimes|array',
                'ui_preferences' => 'sometimes|array',
                'workflow_preferences' => 'sometimes|array',
                'default_ai_model' => 'sometimes|string|max:50',
                'auto_save_interval' => 'sometimes|integer|min:10|max:300',
                'show_tutorials' => 'sometimes|boolean'
            ];

            $messages = [
                'language.in' => '语言设置无效',
                'timezone.timezone' => '时区设置无效',
                'auto_save_interval.min' => '自动保存间隔不能少于10秒',
                'auto_save_interval.max' => '自动保存间隔不能超过300秒'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->userService->updatePreferences($user->id, $request->only([
                'language', 'timezone', 'email_notifications', 'push_notifications',
                'ai_preferences', 'ui_preferences', 'workflow_preferences',
                'default_ai_model', 'auto_save_interval', 'show_tutorials'
            ]));

            // 使用统一返回格式
            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message']);
            }
        } catch (\Exception $e) {
            Log::error('偏好设置更新失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '偏好设置更新失败');
        }
    }

    /**
     * @ApiTitle(获取用户偏好设置)
     * @ApiSummary(获取用户当前偏好设置)
     * @ApiMethod(GET)
     * @ApiRoute(/api/user/preferences)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="偏好设置数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "language": "zh-CN",
     *     "timezone": "Asia/Shanghai",
     *     "email_notifications": true,
     *     "push_notifications": true,
     *     "ai_preferences": {},
     *     "ui_preferences": {},
     *     "workflow_preferences": {},
     *     "default_ai_model": null,
     *     "auto_save_interval": 30,
     *     "show_tutorials": true
     *   }
     * })
     */
    public function getPreferences(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $preference = UserPreference::where('user_id', $user->id)->first();

            if (!$preference) {
                // 如果用户没有偏好设置，创建默认设置
                $preference = UserPreference::create(['user_id' => $user->id]);
            }

            $data = [
                'language' => $preference->language,
                'timezone' => $preference->timezone,
                'email_notifications' => $preference->email_notifications,
                'push_notifications' => $preference->push_notifications,
                'ai_preferences' => $preference->ai_preferences ?? [],
                'ui_preferences' => $preference->ui_preferences ?? [],
                'workflow_preferences' => $preference->workflow_preferences ?? [],
                'default_ai_model' => $preference->default_ai_model,
                'auto_save_interval' => $preference->auto_save_interval,
                'show_tutorials' => $preference->show_tutorials
            ];

            return $this->successResponse($data, 'success');
        } catch (\Exception $e) {
            Log::error('获取偏好设置失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取偏好设置失败');
        }
    }
}
