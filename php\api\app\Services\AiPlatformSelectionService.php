<?php

namespace App\Services;

use App\Models\AiModelConfig;
use App\Models\UserModelPreference;
use App\Models\PlatformPerformanceMetric;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AiPlatformSelectionService
{
    // 🔧 修正：业务平台映射配置（基于CogniAud审计建议）
    protected array $businessPlatformMap = [
        'image' => ['liblib', 'kling', 'minimax'],        // ✅ 三平台支持
        'video' => ['kling', 'minimax'],                  // ✅ 双平台支持
        'story' => ['deepseek', 'minimax'],               // ✅ 双平台支持
        'character' => ['liblib', 'kling', 'minimax'],    // ✅ 三平台支持
        'style' => ['liblib', 'kling', 'minimax'],        // ✅ 三平台支持
        'voice' => ['volcengine', 'minimax'],             // 🔧 修正：火山引擎豆包优先
        'sound' => ['volcengine', 'minimax'],             // ✅ 火山引擎豆包优先
        'music' => ['minimax']                            // ✅ 唯一平台
    ];

    /**
     * 智能选择最佳平台
     */
    public function selectOptimalPlatform(
        string $businessType,
        int $userId,
        array $criteria = []
    ): array {
        try {
            // 🔧 新增：配置验证
            if (!$this->validateBusinessType($businessType)) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '不支持的业务类型: ' . $businessType,
                    'data' => [
                        'supported_types' => array_keys($this->businessPlatformMap)
                    ]
                ];
            }

            // 获取可用平台
            $availablePlatforms = $this->getAvailablePlatforms($businessType);
            
            if (empty($availablePlatforms)) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '当前没有可用的平台',
                    'data' => [
                        'business_type' => $businessType,
                        'expected_platforms' => $this->businessPlatformMap[$businessType]
                    ]
                ];
            }

            // 获取用户偏好
            $userPreference = $this->getUserPreference($userId, $businessType);
            
            // 获取平台性能数据
            $performanceData = $this->getPlatformPerformanceData($businessType);
            
            // 计算平台评分
            $platformScores = $this->calculatePlatformScores(
                $availablePlatforms,
                $userPreference,
                $performanceData,
                $criteria
            );

            // 选择最佳平台
            $selectedPlatform = $this->selectBestPlatform($platformScores);
            
            // 准备备选平台
            $alternatives = $this->prepareAlternatives($platformScores, $selectedPlatform);

            // 🔧 新增：记录选择事件
            $this->recordSelectionEvent($businessType, $userId, $selectedPlatform, $alternatives);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '平台选择成功',
                'data' => [
                    'business_type' => $businessType,
                    'selected_platform' => $selectedPlatform,
                    'alternatives' => $alternatives,
                    'selection_criteria' => $criteria,
                    'selection_time' => now()->toISOString()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('AI平台选择失败', [
                'business_type' => $businessType,
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => 'AI平台选择失败: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 🔧 新增：验证业务类型
     */
    public function validateBusinessType(string $businessType): bool
    {
        return array_key_exists($businessType, $this->businessPlatformMap);
    }

    /**
     * 获取可用平台
     */
    protected function getAvailablePlatforms(string $businessType): array
    {
        $expectedPlatforms = $this->businessPlatformMap[$businessType] ?? [];
        $availablePlatforms = [];

        foreach ($expectedPlatforms as $platform) {
            $models = AiModelConfig::where('platform', $platform)
                ->where('model_type', $businessType)
                ->where('is_active', true)
                ->where('health_status', 'healthy')
                ->get();

            if ($models->isNotEmpty()) {
                $availablePlatforms[] = [
                    'platform' => $platform,
                    'models' => $models->toArray(),
                    'priority' => array_search($platform, $expectedPlatforms) + 1 // 🔧 修正：基于顺序的优先级
                ];
            }
        }

        return $availablePlatforms;
    }

    /**
     * 获取用户偏好
     */
    protected function getUserPreference(int $userId, string $businessType): ?UserModelPreference
    {
        return UserModelPreference::where('user_id', $userId)
            ->where('business_type', $businessType)
            ->first();
    }

    /**
     * 获取平台性能数据
     */
    protected function getPlatformPerformanceData(string $businessType): array
    {
        // 🔧 修正：优化缓存键命名
        $cacheKey = "ai_platform_performance_{$businessType}";
        
        return Cache::remember($cacheKey, 300, function () use ($businessType) {
            return PlatformPerformanceMetric::where('business_type', $businessType)
                ->where('metric_date', '>=', now()->subDays(7))
                ->get()
                ->groupBy('platform')
                ->map(function ($metrics) {
                    return [
                        'avg_response_time' => $metrics->avg('response_time_avg'),
                        'avg_success_rate' => $metrics->avg('success_rate'),
                        'avg_cost_score' => $metrics->avg('cost_score'),
                        'avg_quality_score' => $metrics->avg('quality_score'),
                        'avg_uptime' => $metrics->avg('uptime_percentage')
                    ];
                })
                ->toArray();
        });
    }

    /**
     * 计算平台评分
     */
    protected function calculatePlatformScores(
        array $availablePlatforms,
        ?UserModelPreference $userPreference,
        array $performanceData,
        array $criteria
    ): array {
        $scores = [];

        foreach ($availablePlatforms as $platformData) {
            $platform = $platformData['platform'];
            $performance = $performanceData[$platform] ?? [];
            
            $score = $this->calculateSinglePlatformScore(
                $platform,
                $platformData['priority'], // 🔧 新增：考虑平台优先级
                $performance,
                $userPreference,
                $criteria
            );

            $scores[$platform] = [
                'platform' => $platform,
                'score' => $score,
                'priority' => $platformData['priority'],
                'models' => $platformData['models'],
                'performance' => $performance
            ];
        }

        // 按评分排序
        uasort($scores, function ($a, $b) {
            // 🔧 修正：优先考虑评分，其次考虑优先级
            if (abs($a['score'] - $b['score']) < 0.1) {
                return $a['priority'] <=> $b['priority']; // 优先级低的排前面
            }
            return $b['score'] <=> $a['score'];
        });

        return $scores;
    }

    /**
     * 计算单个平台评分
     */
    protected function calculateSinglePlatformScore(
        string $platform,
        int $priority,
        array $performance,
        ?UserModelPreference $userPreference,
        array $criteria
    ): float {
        $score = 0;
        
        // 🔧 修正：调整权重分配
        $weights = [
            'priority' => 0.3,      // 🔧 新增：平台优先级权重
            'performance' => 0.3,   // 性能权重
            'user_preference' => 0.2, // 用户偏好权重
            'cost' => 0.1,          // 成本权重
            'availability' => 0.1   // 可用性权重
        ];

        // 🔧 新增：平台优先级评分（优先级越高，评分越高）
        $priorityScore = max(0, (10 - $priority) / 10); // 转换为0-1分数
        $score += $priorityScore * $weights['priority'];

        // 性能评分
        if (!empty($performance)) {
            $performanceScore = (
                ($performance['avg_success_rate'] ?? 80) / 100 * 0.3 +
                (1 - min(($performance['avg_response_time'] ?? 5) / 10, 1)) * 0.3 +
                ($performance['avg_quality_score'] ?? 8) / 10 * 0.2 +
                ($performance['avg_uptime'] ?? 95) / 100 * 0.2
            );
            $score += $performanceScore * $weights['performance'];
        } else {
            // 🔧 新增：无性能数据时的默认评分
            $score += 0.7 * $weights['performance']; // 给予中等评分
        }

        // 用户偏好评分
        if ($userPreference) {
            $preferenceScore = $userPreference->getPlatformPriority($platform) / 100;
            $score += $preferenceScore * $weights['user_preference'];
        } else {
            // 🔧 新增：无用户偏好时的默认评分
            $score += 0.5 * $weights['user_preference'];
        }

        // 成本评分
        $costScore = ($performance['avg_cost_score'] ?? 7) / 10;
        $score += $costScore * $weights['cost'];

        // 可用性评分
        $availabilityScore = ($performance['avg_uptime'] ?? 95) / 100;
        $score += $availabilityScore * $weights['availability'];

        return min($score, 1.0);
    }

    /**
     * 选择最佳平台
     */
    protected function selectBestPlatform(array $platformScores): array
    {
        $bestPlatform = reset($platformScores);
        
        return [
            'platform' => $bestPlatform['platform'],
            'score' => $bestPlatform['score'],
            'priority' => $bestPlatform['priority'],
            'models' => $bestPlatform['models'],
            'selection_reason' => $this->generateSelectionReason($bestPlatform)
        ];
    }

    /**
     * 准备备选平台
     */
    protected function prepareAlternatives(array $platformScores, array $selectedPlatform): array
    {
        $alternatives = [];
        
        foreach ($platformScores as $platform => $data) {
            if ($platform !== $selectedPlatform['platform']) {
                $alternatives[] = [
                    'platform' => $platform,
                    'score' => $data['score'],
                    'priority' => $data['priority'],
                    'switch_reason' => $this->generateSwitchReason($data, $selectedPlatform)
                ];
            }
        }

        return array_slice($alternatives, 0, 3); // 最多3个备选
    }

    /**
     * 生成选择原因
     */
    protected function generateSelectionReason(array $platformData): string
    {
        $score = $platformData['score'];
        $priority = $platformData['priority'];
        
        if ($priority === 1) {
            return '首选平台，综合性能最优';
        } elseif ($score >= 0.9) {
            return '综合性能最优，各项指标均表现优秀';
        } elseif ($score >= 0.8) {
            return '性能表现良好，符合业务需求';
        } elseif ($score >= 0.7) {
            return '基本满足需求，性价比较高';
        } else {
            return '当前可用的最佳选择';
        }
    }

    /**
     * 生成切换原因
     */
    protected function generateSwitchReason(array $alternativeData, array $selectedData): string
    {
        $scoreDiff = $alternativeData['score'] - $selectedData['score'];
        $priorityDiff = $alternativeData['priority'] - $selectedData['priority'];
        
        if ($priorityDiff < 0) {
            return '更高优先级平台，可获得更好的服务质量';
        } elseif ($scoreDiff > -0.1) {
            return '性能相近，可作为备选方案';
        } elseif ($scoreDiff > -0.2) {
            return '成本更低，适合预算优先场景';
        } else {
            return '备选方案，可在主平台不可用时使用';
        }
    }

    /**
     * 🔧 新增：记录选择事件
     */
    protected function recordSelectionEvent(
        string $businessType,
        int $userId,
        array $selectedPlatform,
        array $alternatives
    ): void {
        Log::info('AI平台选择事件', [
            'user_id' => $userId,
            'business_type' => $businessType,
            'selected_platform' => $selectedPlatform['platform'],
            'selected_score' => $selectedPlatform['score'],
            'selected_priority' => $selectedPlatform['priority'],
            'alternatives_count' => count($alternatives),
            'selection_reason' => $selectedPlatform['selection_reason'],
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * 🔧 新增：获取业务类型支持的平台列表
     */
    public function getSupportedPlatforms(string $businessType): array
    {
        return $this->businessPlatformMap[$businessType] ?? [];
    }

    /**
     * 🔧 新增：验证平台是否支持指定业务类型
     */
    public function isPlatformSupported(string $platform, string $businessType): bool
    {
        $supportedPlatforms = $this->getSupportedPlatforms($businessType);
        return in_array($platform, $supportedPlatforms);
    }
}
