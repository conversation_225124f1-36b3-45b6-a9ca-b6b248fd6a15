<?php

namespace App\Services;

use App\Enums\ApiCodeEnum;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use App\Models\CharacterLibrary;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

/**
 * 图像生成服务
 * 第2D1阶段：图像生成模块
 */
class ImageService
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }

    /**
     * 生成图像
     */
    public function generateImage(int $userId, string $prompt, ?int $characterId = null, ?int $projectId = null, array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取角色信息
            $character = null;
            if ($characterId) {
                $character = CharacterLibrary::find($characterId);
                if (!$character) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '角色不存在',
                        'data' => []
                    ];
                }
            }

            // 获取模型配置
            $platform = $generationParams['platform'] ?? 'liblib';
            $model = $this->aiModelService->getModelByPlatform($platform, AiModelConfig::TYPE_IMAGE_GENERATION);

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的图像生成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '图像生成服务当前不可用',
                    'data' => []
                ];
            }

            // 构建增强提示词
            $enhancedPrompt = $this->buildImagePrompt($prompt, $character, $generationParams);

            // 计算预估成本
            $estimatedCost = $this->calculateImageCost($model, $generationParams);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'image_generation',
                null,
                600 // 10分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => AiGenerationTask::TYPE_IMAGE_GENERATION,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'enhanced_prompt' => $enhancedPrompt,
                    'character_id' => $characterId,
                    'aspect_ratio' => $generationParams['aspect_ratio'] ?? '16:9',
                    'quality' => $generationParams['quality'] ?? 'standard',
                    'style' => $generationParams['style'] ?? null
                ],
                'generation_params' => $generationParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行生成任务
            $this->executeImageGeneration($task);

            Log::info('图像生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '图像生成任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'platform' => $platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('图像生成失败', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '图像生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取图像生成状态
     */
    public function getImageStatus(int $taskId, int $userId): array
    {
        $task = AiGenerationTask::where('id', $taskId)
            ->where('user_id', $userId)
            ->where('task_type', AiGenerationTask::TYPE_IMAGE_GENERATION)
            ->first();

        if (!$task) {
            return [
                'code' => ApiCodeEnum::NOT_FOUND,
                'message' => '任务不存在',
                'data' => []
            ];
        }

        $data = [
            'id' => $task->id,
            'task_type' => $task->task_type,
            'status' => $task->status,
            'platform' => $task->platform,
            'cost' => $task->cost,
            'processing_time_ms' => $task->processing_time_ms,
            'created_at' => $task->created_at->format('Y-m-d H:i:s'),
            'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
        ];

        // 如果任务完成，添加生成结果
        if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
            $data['image_url'] = $task->output_data['image_url'] ?? '';
            $data['thumbnail_url'] = $task->output_data['thumbnail_url'] ?? '';
        }

        // 如果任务失败，添加错误信息
        if ($task->status === AiGenerationTask::STATUS_FAILED) {
            $data['error_message'] = $task->error_message;
        }

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'success',
            'data' => $data
        ];
    }

    /**
     * 获取图像生成结果
     */
    public function getImageResult(int $taskId, int $userId): array
    {
        $task = AiGenerationTask::where('id', $taskId)
            ->where('user_id', $userId)
            ->where('task_type', AiGenerationTask::TYPE_IMAGE_GENERATION)
            ->first();

        if (!$task) {
            return [
                'code' => ApiCodeEnum::NOT_FOUND,
                'message' => '任务不存在',
                'data' => []
            ];
        }

        if ($task->status !== AiGenerationTask::STATUS_COMPLETED) {
            return [
                'code' => ApiCodeEnum::FAIL,
                'message' => '任务尚未完成',
                'data' => []
            ];
        }

        $data = [
            'task_id' => $task->id,
            'image_url' => $task->output_data['image_url'] ?? '',
            'thumbnail_url' => $task->output_data['thumbnail_url'] ?? '',
            'metadata' => $task->output_data['metadata'] ?? [],
            'download_info' => [
                'direct_url' => $task->output_data['image_url'] ?? '',
                'expires_at' => Carbon::now()->addDays(7)->format('Y-m-d H:i:s')
            ]
        ];

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'success',
            'data' => $data
        ];
    }

    /**
     * 批量生成图像
     */
    public function batchGenerateImages(int $userId, array $prompts, ?int $projectId = null, array $commonParams = []): array
    {
        try {
            DB::beginTransaction();

            $batchId = 'batch_' . time() . '_' . $userId;
            $taskIds = [];
            $totalCost = 0;

            foreach ($prompts as $prompt) {
                $result = $this->generateImage($userId, $prompt, null, $projectId, $commonParams);
                
                if ($result['code'] === ApiCodeEnum::SUCCESS) {
                    $taskIds[] = $result['data']['task_id'];
                    $totalCost += $result['data']['estimated_cost'];
                } else {
                    DB::rollBack();
                    return $result;
                }
            }

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '批量图像生成任务创建成功',
                'data' => [
                    'batch_id' => $batchId,
                    'task_ids' => $taskIds,
                    'total_count' => count($taskIds),
                    'estimated_cost' => number_format($totalCost, 4)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批量图像生成失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量图像生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 构建图像提示词
     */
    private function buildImagePrompt(string $prompt, ?CharacterLibrary $character, array $params): string
    {
        $enhancedPrompt = $prompt;

        // 添加角色信息
        if ($character) {
            $enhancedPrompt .= "\n\n角色描述：" . $character->description;
            if ($character->appearance) {
                $enhancedPrompt .= "\n外观特征：" . $character->appearance;
            }
        }

        // 添加风格信息
        if (!empty($params['style'])) {
            $enhancedPrompt .= "\n\n风格要求：" . $params['style'];
        }

        // 添加质量要求
        $quality = $params['quality'] ?? 'standard';
        if ($quality === 'hd') {
            $enhancedPrompt .= "\n\n高清画质，细节丰富，专业摄影";
        }

        return $enhancedPrompt;
    }

    /**
     * 计算图像生成成本
     */
    private function calculateImageCost(AiModelConfig $model, array $params): float
    {
        $baseCost = $model->cost_per_token;
        
        // 质量影响成本
        $quality = $params['quality'] ?? 'standard';
        $qualityMultiplier = $quality === 'hd' ? 2.0 : 1.0;

        return round($baseCost * $qualityMultiplier, 4);
    }

    /**
     * 执行图像生成
     */
    private function executeImageGeneration(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 调用AI服务
            $result = $this->callAiService($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'image_generation', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $this->pointsService->refundPoints($task->user_id, $task->cost, 'image_generation_failed', $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'image_generation_error', $task->id);

            Log::error('图像生成执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 调用AI服务
     */
    private function callAiService(AiGenerationTask $task): array
    {
        try {
            $aiApiUrl = config('ai.api_url', 'https://aiapi.tiptop.cn');
            $platformConfig = config("ai.platforms.{$task->platform}");
            $endpoint = $platformConfig['endpoint'] ?? '/liblib/v1/image_generation';

            $timeout = $platformConfig['timeout'] ?? config('ai.timeout', 30);
            $response = Http::timeout($timeout)->post($aiApiUrl . $endpoint, [
                'prompt' => $task->input_data['enhanced_prompt'],
                'aspect_ratio' => $task->input_data['aspect_ratio'],
                'quality' => $task->input_data['quality']
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'data' => [
                        'image_url' => $data['data']['image_url'] ?? '',
                        'thumbnail_url' => $data['data']['thumbnail_url'] ?? '',
                        'metadata' => [
                            'width' => $data['data']['width'] ?? 1024,
                            'height' => $data['data']['height'] ?? 1024,
                            'format' => 'jpg',
                            'file_size' => $data['data']['file_size'] ?? '2.5MB'
                        ]
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage()
            ];
        }
    }
}
